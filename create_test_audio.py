#!/usr/bin/env python3
"""Create a test audio file for voice changer testing."""

import sys
import subprocess
from pathlib import Path

def create_test_audio():
    """Create a simple test audio file using text-to-speech."""
    
    # Test text
    test_text = "Hello, this is a test of the voice changer application. I am speaking clearly so that the speech recognition can understand me perfectly."
    
    print("🎤 Creating test audio file...")
    
    # Try different methods to create audio
    methods = [
        # Method 1: Using espeak (common on Linux)
        {
            'command': ['espeak', '-w', 'test_input.wav', test_text],
            'description': 'Using espeak'
        },
        # Method 2: Using festival
        {
            'command': ['echo', test_text, '|', 'festival', '--tts', '--otype', 'wav', '--output', 'test_input.wav'],
            'description': 'Using festival'
        },
        # Method 3: Using say (macOS)
        {
            'command': ['say', '-o', 'test_input.wav', test_text],
            'description': 'Using say (macOS)'
        }
    ]
    
    for method in methods:
        try:
            print(f"Trying: {method['description']}")
            if method['command'][0] == 'echo':
                # Special handling for festival
                subprocess.run(f"echo '{test_text}' | festival --tts --otype wav --output test_input.wav", 
                             shell=True, check=True)
            else:
                subprocess.run(method['command'], check=True)
            
            if Path('test_input.wav').exists():
                print(f"✅ Test audio created successfully using {method['description']}")
                return True
                
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {method['description']} not available")
            continue
    
    print("❌ Could not create test audio with available tools")
    return False

def download_sample_audio():
    """Download a sample audio file from the internet."""
    try:
        import requests
        
        # Sample audio URLs (public domain)
        sample_urls = [
            {
                'url': 'https://www2.cs.uic.edu/~i101/SoundFiles/BabyElephantWalk60.wav',
                'filename': 'sample_music.wav',
                'description': 'Music sample'
            },
            {
                'url': 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
                'filename': 'sample_bell.wav', 
                'description': 'Bell sound'
            }
        ]
        
        for sample in sample_urls:
            try:
                print(f"Downloading {sample['description']}...")
                response = requests.get(sample['url'], timeout=10)
                response.raise_for_status()
                
                with open(sample['filename'], 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ Downloaded {sample['filename']}")
                return True
                
            except Exception as e:
                print(f"❌ Failed to download {sample['description']}: {e}")
                continue
                
    except ImportError:
        print("❌ requests library not available for downloading")
    
    return False

def main():
    """Main function."""
    print("🎵 Voice Changer Test Audio Creator")
    print("=" * 40)
    
    # Try to create test audio
    if create_test_audio():
        return
    
    # If that fails, try downloading
    print("\nTrying to download sample audio...")
    if download_sample_audio():
        return
    
    # If all fails, provide instructions
    print("\n📝 Manual Instructions:")
    print("Since automatic audio creation failed, please:")
    print("1. Record a short audio file (5-30 seconds) saying something like:")
    print("   'Hello, this is a test of the voice changer application'")
    print("2. Save it as 'test_input.wav' or 'test_input.mp3'")
    print("3. Or download any speech audio file from the internet")
    print("4. Supported formats: .wav, .mp3, .m4a, .flac, .ogg")

if __name__ == "__main__":
    main()
