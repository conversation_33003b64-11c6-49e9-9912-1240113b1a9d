# 🎤 Voice Changer

A powerful voice transformation application that uses AI to change voices while preserving speech content. Built with PlayHT's advanced text-to-speech API and OpenAI's Whisper for speech recognition.

## ✨ Features

### Core Voice Transformation
- **High-Quality Voice Conversion**: Transform any voice to sound like different speakers
- **Multiple Voice Options**: Choose from hundreds of pre-built voices or create custom voice clones
- **Preserve Speech Content**: Maintains the original words and meaning while changing voice characteristics
- **Multiple Audio Formats**: Support for MP3, WAV, FLAC, OGG, and more

### Voice Cloning
- **Custom Voice Creation**: Clone any voice from a 2-second to 1-hour audio sample
- **Instant Voice Cloning**: Quick voice replication using PlayHT's advanced AI
- **Voice Management**: List, create, and delete custom voice clones

### User Interfaces
- **Web Interface**: Beautiful Streamlit-based web app with drag-and-drop functionality
- **Command Line Interface**: Powerful CLI for batch processing and automation
- **Real-time Preview**: Generate 30-second previews before full processing

### Advanced Features
- **Batch Processing**: Process multiple audio files simultaneously
- **Cost Estimation**: Estimate processing costs before running
- **Progress Tracking**: Real-time progress updates during processing
- **Quality Controls**: Adjustable sample rates and audio formats

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd play_ht

# Install dependencies
pip install -e .
```

### 2. Setup API Keys

Create a `.env` file in the project root:

```env
# PlayHT API Configuration
PLAY_HT_USER_ID=your_user_id_here
PLAY_HT_API_KEY=your_api_key_here

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
```

**Get your API keys:**
- **PlayHT**: Visit [PlayHT API Access](https://play.ht/studio/api-access)
- **OpenAI**: Visit [OpenAI API Keys](https://platform.openai.com/api-keys)

### 3. Run the Application

```bash
# Interactive launcher
python main.py

# Or run directly:
# Web interface
streamlit run src/web_app.py

# CLI interface
python -m src.cli --help
```

## 📖 Usage Guide

### Web Interface

1. **Start the web app**: `streamlit run src/web_app.py`
2. **Upload audio file**: Drag and drop or browse for your audio file
3. **Select target voice**: Choose from pre-built voices or your custom clones
4. **Configure settings**: Adjust voice engine, format, and quality settings
5. **Process**: Click "Process Audio" and download the result

### Command Line Interface

#### Basic Voice Change
```bash
# Change voice of an audio file
python -m src.cli change input.wav "voice_id_here" --output output.mp3

# Generate a preview first
python -m src.cli change input.wav "voice_id_here" --preview
```

#### Batch Processing
```bash
# Process all audio files in a directory
python -m src.cli batch ./input_folder "voice_id_here" ./output_folder
```

#### Voice Management
```bash
# List available voices
python -m src.cli list-voices

# Create a voice clone
python -m src.cli clone-voice sample.wav "My Custom Voice"

# Estimate processing cost
python -m src.cli estimate-cost input.wav
```

### Python API

```python
from src.voice_changer import VoiceChanger

# Initialize
voice_changer = VoiceChanger()

# Change voice
output_path = voice_changer.change_voice(
    input_audio_path="input.wav",
    target_voice="voice_id_here",
    output_format="mp3"
)

# Create voice clone
clone_info = voice_changer.create_voice_clone(
    sample_audio_path="sample.wav",
    voice_name="My Voice"
)
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PLAY_HT_USER_ID` | Your PlayHT user ID | Required |
| `PLAY_HT_API_KEY` | Your PlayHT API key | Required |
| `OPENAI_API_KEY` | Your OpenAI API key | Required |
| `DEFAULT_VOICE_ENGINE` | Default voice engine | `Play3.0-mini-http` |
| `DEFAULT_AUDIO_FORMAT` | Default output format | `mp3` |
| `DEFAULT_SAMPLE_RATE` | Default sample rate | `24000` |
| `MAX_FILE_SIZE_MB` | Maximum file size | `50` |
| `MAX_AUDIO_DURATION_SECONDS` | Maximum audio duration | `3600` |

### Voice Engines

| Engine | Description |
|--------|-------------|
| `Play3.0-mini-http` | Multilingual, fast processing |
| `Play3.0-mini-ws` | Multilingual, low latency WebSocket |
| `PlayDialog-http` | Advanced dialogues and conversations |
| `PlayDialog-ws` | Advanced dialogues, low latency |
| `PlayHT2.0-turbo` | Legacy English-only model |

## 📁 Project Structure

```
play_ht/
├── src/
│   ├── voice_changer/
│   │   ├── __init__.py
│   │   ├── core.py              # Main voice changer logic
│   │   ├── playht_client.py     # PlayHT API integration
│   │   ├── speech_to_text.py    # Whisper integration
│   │   └── audio_utils.py       # Audio processing utilities
│   ├── config.py                # Configuration management
│   ├── cli.py                   # Command-line interface
│   └── web_app.py              # Streamlit web interface
├── main.py                      # Main application launcher
├── pyproject.toml              # Project dependencies
├── .env.example                # Environment variables template
└── README.md                   # This file
```

## 🔄 How It Works

The voice changer uses a sophisticated pipeline:

1. **Audio Input**: Accept various audio formats (MP3, WAV, etc.)
2. **Speech-to-Text**: Use OpenAI Whisper to transcribe the audio
3. **Text-to-Speech**: Use PlayHT API to synthesize speech with target voice
4. **Audio Output**: Save the result in desired format

```
Input Audio → Whisper (STT) → Text → PlayHT (TTS) → Output Audio
```

## 💰 Cost Information

### Pricing Estimates
- **OpenAI Whisper**: ~$0.006 per minute of audio
- **PlayHT TTS**: ~$0.0004 per 1000 characters (varies by plan)

### Cost Optimization Tips
- Use preview mode for testing
- Trim silence from audio files
- Choose appropriate quality settings
- Use batch processing for multiple files

## 🛠️ Development

### Setup Development Environment

```bash
# Install in development mode
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black src/
isort src/

# Type checking
mypy src/
```

### Adding New Features

1. **New Voice Engines**: Add to `VOICE_ENGINES` in `config.py`
2. **Audio Formats**: Update `SUPPORTED_INPUT_FORMATS` and `SUPPORTED_OUTPUT_FORMATS`
3. **CLI Commands**: Add new commands to `cli.py`
4. **Web Features**: Add new pages/components to `web_app.py`

## 🐛 Troubleshooting

### Common Issues

**"Missing API keys" error**
- Ensure `.env` file exists with correct API keys
- Check that environment variables are set correctly

**"Audio file too large" error**
- Reduce file size or increase `MAX_FILE_SIZE_MB`
- Use audio compression tools

**"No speech detected" error**
- Ensure audio contains clear speech
- Check audio quality and volume levels
- Try trimming silence

**Import errors**
- Ensure all dependencies are installed: `pip install -e .`
- Check Python version compatibility (3.12+)

### Getting Help

1. Check the logs in `voice_changer.log`
2. Use verbose mode: `python -m src.cli -v`
3. Test with the cost estimator first
4. Try preview mode before full processing

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 🙏 Acknowledgments

- [PlayHT](https://play.ht/) for the amazing text-to-speech API
- [OpenAI](https://openai.com/) for Whisper speech recognition
- [Streamlit](https://streamlit.io/) for the web interface framework