"""Configuration management for the voice changer application."""

import os
import logging
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for the voice changer application."""
    
    # PlayHT API Configuration
    PLAY_HT_USER_ID: str = os.getenv("PLAY_HT_USER_ID", "")
    PLAY_HT_API_KEY: str = os.getenv("PLAY_HT_API_KEY", "")
    
    # OpenAI API Configuration
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    
    # Application Configuration
    DEFAULT_VOICE_ENGINE: str = os.getenv("DEFAULT_VOICE_ENGINE", "Play3.0-mini-http")
    DEFAULT_AUDIO_FORMAT: str = os.getenv("DEFAULT_AUDIO_FORMAT", "mp3")
    DEFAULT_SAMPLE_RATE: int = int(os.getenv("DEFAULT_SAMPLE_RATE", "24000"))
    MAX_FILE_SIZE_MB: int = int(os.getenv("MAX_FILE_SIZE_MB", "50"))
    MAX_AUDIO_DURATION_SECONDS: int = int(os.getenv("MAX_AUDIO_DURATION_SECONDS", "3600"))
    
    # Web App Configuration
    STREAMLIT_SERVER_PORT: int = int(os.getenv("STREAMLIT_SERVER_PORT", "8501"))
    STREAMLIT_SERVER_ADDRESS: str = os.getenv("STREAMLIT_SERVER_ADDRESS", "localhost")
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "voice_changer.log")
    
    @classmethod
    def validate_api_keys(cls) -> tuple[bool, list[str]]:
        """Validate that required API keys are present.
        
        Returns:
            tuple: (is_valid, list_of_missing_keys)
        """
        missing_keys = []
        
        if not cls.PLAY_HT_USER_ID:
            missing_keys.append("PLAY_HT_USER_ID")
        if not cls.PLAY_HT_API_KEY:
            missing_keys.append("PLAY_HT_API_KEY")
        if not cls.OPENAI_API_KEY:
            missing_keys.append("OPENAI_API_KEY")
            
        return len(missing_keys) == 0, missing_keys
    
    @classmethod
    def setup_logging(cls) -> None:
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, cls.LOG_LEVEL.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(cls.LOG_FILE),
                logging.StreamHandler()
            ]
        )


# Supported audio formats
SUPPORTED_INPUT_FORMATS = {
    '.mp3', '.wav', '.m4a', '.flac', '.ogg', '.aac', '.wma'
}

SUPPORTED_OUTPUT_FORMATS = {
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav',
    'flac': 'audio/flac',
    'ogg': 'audio/ogg'
}

# PlayHT voice engines
VOICE_ENGINES = {
    'Play3.0-mini-http': 'Play3.0 Mini (HTTP) - Multilingual, fast',
    'Play3.0-mini-ws': 'Play3.0 Mini (WebSocket) - Multilingual, low latency',
    'PlayDialog-http': 'PlayDialog (HTTP) - Advanced dialogues',
    'PlayDialog-ws': 'PlayDialog (WebSocket) - Advanced dialogues, low latency',
    'PlayHT2.0-turbo': 'PlayHT 2.0 Turbo - Legacy English-only'
}

# Sample rates supported by PlayHT
SAMPLE_RATES = [8000, 16000, 24000, 44100, 48000]
