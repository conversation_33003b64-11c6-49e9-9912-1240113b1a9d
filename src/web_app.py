"""Streamlit web application for the voice changer."""

import streamlit as st
import tempfile
import logging
from pathlib import Path
from typing import Optional
import time
import io

from .config import Config, VOICE_ENGINES, SUPPORTED_INPUT_FORMATS
from .voice_changer import VoiceChanger

# Setup logging
Config.setup_logging()
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Voice Changer",
    page_icon="🎤",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
.main-header {
    font-size: 3rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}
.section-header {
    font-size: 1.5rem;
    color: #333;
    margin-top: 2rem;
    margin-bottom: 1rem;
}
.info-box {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
.success-box {
    background-color: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
.error-box {
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize session state variables."""
    if 'voice_changer' not in st.session_state:
        st.session_state.voice_changer = None
    if 'available_voices' not in st.session_state:
        st.session_state.available_voices = {'prebuilt': [], 'cloned': []}
    if 'processing' not in st.session_state:
        st.session_state.processing = False


def check_api_keys():
    """Check if API keys are configured."""
    is_valid, missing_keys = Config.validate_api_keys()
    if not is_valid:
        st.error(f"Missing API keys: {', '.join(missing_keys)}")
        st.info("Please set the required environment variables or create a .env file with your API keys.")
        st.code("""
# Create a .env file with:
PLAY_HT_USER_ID=your_user_id_here
PLAY_HT_API_KEY=your_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
        """)
        return False
    return True


def initialize_voice_changer():
    """Initialize the voice changer if not already done."""
    if st.session_state.voice_changer is None:
        try:
            with st.spinner("Initializing voice changer..."):
                st.session_state.voice_changer = VoiceChanger()
                # Load available voices
                voices = st.session_state.voice_changer.get_available_voices()
                st.session_state.available_voices = voices
            st.success("Voice changer initialized successfully!")
        except Exception as e:
            st.error(f"Failed to initialize voice changer: {e}")
            return False
    return True


def display_voice_selector():
    """Display voice selection interface."""
    st.markdown('<div class="section-header">🎭 Select Target Voice</div>', unsafe_allow_html=True)

    voice_type = st.radio("Voice Type", ["Pre-built Voices", "Cloned Voices"], horizontal=True)

    if voice_type == "Pre-built Voices":
        voices = st.session_state.available_voices['prebuilt']
        if voices:
            voice_options = {f"{v.get('name', 'Unknown')} ({v.get('language', 'Unknown')})": v.get('id', '')
                           for v in voices}
            selected_voice_name = st.selectbox("Choose a pre-built voice:", list(voice_options.keys()))
            selected_voice_id = voice_options[selected_voice_name]
        else:
            st.warning("No pre-built voices available")
            selected_voice_id = None
    else:
        voices = st.session_state.available_voices['cloned']
        if voices:
            voice_options = {v.get('name', 'Unknown'): v.get('id', '') for v in voices}
            selected_voice_name = st.selectbox("Choose a cloned voice:", list(voice_options.keys()))
            selected_voice_id = voice_options[selected_voice_name]
        else:
            st.warning("No cloned voices available")
            selected_voice_id = None

    return selected_voice_id


def display_audio_upload():
    """Display audio upload interface."""
    st.markdown('<div class="section-header">🎵 Upload Audio File</div>', unsafe_allow_html=True)

    uploaded_file = st.file_uploader(
        "Choose an audio file",
        type=list(SUPPORTED_INPUT_FORMATS),
        help=f"Supported formats: {', '.join(SUPPORTED_INPUT_FORMATS)}"
    )

    if uploaded_file is not None:
        # Display file info
        file_size_mb = len(uploaded_file.getvalue()) / (1024 * 1024)
        st.info(f"File: {uploaded_file.name} ({file_size_mb:.1f} MB)")

        # Audio player
        st.audio(uploaded_file.getvalue())

        return uploaded_file

    return None


def display_settings():
    """Display processing settings."""
    st.markdown('<div class="section-header">⚙️ Processing Settings</div>', unsafe_allow_html=True)

    col1, col2 = st.columns(2)

    with col1:
        voice_engine = st.selectbox(
            "Voice Engine",
            list(VOICE_ENGINES.keys()),
            index=list(VOICE_ENGINES.keys()).index(Config.DEFAULT_VOICE_ENGINE),
            help="Choose the voice engine for processing"
        )

        output_format = st.selectbox(
            "Output Format",
            ["mp3", "wav", "flac", "ogg"],
            index=0,
            help="Choose the output audio format"
        )

    with col2:
        sample_rate = st.selectbox(
            "Sample Rate",
            [8000, 16000, 24000, 44100, 48000],
            index=2,  # Default to 24000
            help="Choose the output sample rate"
        )

        preview_mode = st.checkbox(
            "Preview Mode (30 seconds)",
            help="Generate a 30-second preview instead of processing the full audio"
        )

    return voice_engine, output_format, sample_rate, preview_mode


def process_audio(uploaded_file, target_voice, voice_engine, output_format, sample_rate, preview_mode):
    """Process the uploaded audio file."""
    if st.session_state.processing:
        st.warning("Processing in progress...")
        return

    st.session_state.processing = True

    try:
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(uploaded_file.name).suffix) as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            tmp_file_path = tmp_file.name

        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()

        def progress_callback(message: str, progress: float):
            progress_bar.progress(progress)
            status_text.text(message)

        # Process the audio
        start_time = time.time()

        if preview_mode:
            status_text.text("Generating preview...")
            result_data = st.session_state.voice_changer.preview_voice_change(
                input_audio_path=tmp_file_path,
                target_voice=target_voice,
                voice_engine=voice_engine
            )

            # For preview, result_data is the path to the file
            with open(result_data, 'rb') as f:
                result_audio = f.read()

            output_filename = f"preview_{uploaded_file.name}"
        else:
            result_path = st.session_state.voice_changer.change_voice(
                input_audio_path=tmp_file_path,
                target_voice=target_voice,
                voice_engine=voice_engine,
                output_format=output_format,
                sample_rate=sample_rate,
                progress_callback=progress_callback
            )

            # Read the result file
            with open(result_path, 'rb') as f:
                result_audio = f.read()

            output_filename = f"voice_changed_{uploaded_file.name}"

        processing_time = time.time() - start_time

        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()

        # Display results
        st.success(f"Processing completed in {processing_time:.1f} seconds!")

        # Audio player for result
        st.audio(result_audio)

        # Download button
        st.download_button(
            label="Download Result",
            data=result_audio,
            file_name=output_filename,
            mime=f"audio/{output_format}"
        )

        # Clean up temporary files
        Path(tmp_file_path).unlink(missing_ok=True)
        if not preview_mode:
            Path(result_path).unlink(missing_ok=True)

    except Exception as e:
        st.error(f"Error processing audio: {e}")
        logger.error(f"Web app processing error: {e}")

    finally:
        st.session_state.processing = False


def display_voice_cloning():
    """Display voice cloning interface."""
    st.markdown('<div class="section-header">🎭 Create Voice Clone</div>', unsafe_allow_html=True)

    with st.expander("Create New Voice Clone"):
        st.info("Upload a sample audio file (2 seconds to 1 hour) to create a voice clone.")

        clone_file = st.file_uploader(
            "Sample audio for cloning",
            type=list(SUPPORTED_INPUT_FORMATS),
            key="clone_upload"
        )

        voice_name = st.text_input("Voice name", placeholder="Enter a name for your voice clone")

        if st.button("Create Voice Clone") and clone_file and voice_name:
            try:
                with st.spinner("Creating voice clone..."):
                    # Save uploaded file
                    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(clone_file.name).suffix) as tmp_file:
                        tmp_file.write(clone_file.getvalue())
                        tmp_file_path = tmp_file.name

                    # Create voice clone
                    result = st.session_state.voice_changer.create_voice_clone(tmp_file_path, voice_name)

                    st.success(f"Voice clone '{voice_name}' created successfully!")
                    st.json(result)

                    # Refresh available voices
                    voices = st.session_state.voice_changer.get_available_voices()
                    st.session_state.available_voices = voices

                    # Clean up
                    Path(tmp_file_path).unlink(missing_ok=True)

            except Exception as e:
                st.error(f"Error creating voice clone: {e}")


def display_cost_estimator():
    """Display cost estimation interface."""
    st.markdown('<div class="section-header">💰 Cost Estimator</div>', unsafe_allow_html=True)

    with st.expander("Estimate Processing Cost"):
        estimate_file = st.file_uploader(
            "Upload audio file for cost estimation",
            type=list(SUPPORTED_INPUT_FORMATS),
            key="estimate_upload"
        )

        if estimate_file:
            try:
                with tempfile.NamedTemporaryFile(delete=False, suffix=Path(estimate_file.name).suffix) as tmp_file:
                    tmp_file.write(estimate_file.getvalue())
                    tmp_file_path = tmp_file.name

                cost_info = st.session_state.voice_changer.estimate_processing_cost(tmp_file_path)

                col1, col2 = st.columns(2)
                with col1:
                    st.metric("Audio Duration", f"{cost_info['audio_duration_seconds']:.1f}s")
                    st.metric("Estimated Characters", f"{cost_info['estimated_characters']:,}")

                with col2:
                    st.metric("Transcription Cost", f"${cost_info['transcription_cost_usd']:.4f}")
                    st.metric("TTS Cost", f"${cost_info['tts_cost_usd']:.4f}")

                st.metric("Total Estimated Cost", f"${cost_info['total_estimated_cost_usd']:.4f}")

                # Clean up
                Path(tmp_file_path).unlink(missing_ok=True)

            except Exception as e:
                st.error(f"Error estimating cost: {e}")


def main():
    """Main application function."""
    # Initialize session state
    initialize_session_state()

    # Header
    st.markdown('<div class="main-header">🎤 Voice Changer</div>', unsafe_allow_html=True)
    st.markdown("Transform your voice using AI-powered voice synthesis")

    # Check API keys
    if not check_api_keys():
        return

    # Initialize voice changer
    if not initialize_voice_changer():
        return

    # Sidebar
    with st.sidebar:
        st.header("Navigation")
        page = st.radio("Choose a page:", ["Voice Changer", "Voice Cloning", "Cost Estimator"])

        st.header("About")
        st.info("""
        This application uses:
        - OpenAI Whisper for speech-to-text
        - PlayHT API for text-to-speech
        - Advanced voice cloning technology
        """)

    # Main content based on selected page
    if page == "Voice Changer":
        # Voice selection
        target_voice = display_voice_selector()

        # Audio upload
        uploaded_file = display_audio_upload()

        # Settings
        voice_engine, output_format, sample_rate, preview_mode = display_settings()

        # Process button
        if st.button("🚀 Process Audio", disabled=not (uploaded_file and target_voice)):
            if uploaded_file and target_voice:
                process_audio(uploaded_file, target_voice, voice_engine, output_format, sample_rate, preview_mode)
            else:
                st.warning("Please upload an audio file and select a target voice.")

    elif page == "Voice Cloning":
        display_voice_cloning()

    elif page == "Cost Estimator":
        display_cost_estimator()


if __name__ == "__main__":
    main()
