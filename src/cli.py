"""Command-line interface for the voice changer application."""

import click
import logging
from pathlib import Path
from typing import Optional
from tqdm import tqdm

from .config import Config, VOICE_ENGINES
from .voice_changer import VoiceChanger

# Setup logging
Config.setup_logging()
logger = logging.getLogger(__name__)


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def cli(verbose):
    """Voice Changer CLI - Transform voices using PlayHT API."""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)


@cli.command()
@click.argument('input_file', type=click.Path(exists=True, path_type=Path))
@click.argument('target_voice', type=str)
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output file path')
@click.option('--voice-engine', type=click.Choice(list(VOICE_ENGINES.keys())), 
              default=Config.DEFAULT_VOICE_ENGINE, help='Voice engine to use')
@click.option('--format', 'output_format', type=click.Choice(['mp3', 'wav', 'flac', 'ogg']),
              default='mp3', help='Output audio format')
@click.option('--sample-rate', type=click.Choice(['8000', '16000', '24000', '44100', '48000']),
              default='24000', help='Output sample rate')
@click.option('--preview', is_flag=True, help='Generate a 30-second preview only')
def change(input_file, target_voice, output, voice_engine, output_format, sample_rate, preview):
    """Change the voice of an audio file."""
    try:
        # Validate API keys
        is_valid, missing_keys = Config.validate_api_keys()
        if not is_valid:
            click.echo(f"Error: Missing API keys: {', '.join(missing_keys)}", err=True)
            click.echo("Please set the required environment variables or create a .env file.", err=True)
            return
        
        # Initialize voice changer
        voice_changer = VoiceChanger()
        
        # Progress callback
        def progress_callback(message: str, progress: float):
            click.echo(f"{message} ({progress*100:.1f}%)")
        
        if preview:
            click.echo("Generating preview...")
            audio_data = voice_changer.preview_voice_change(
                input_audio_path=input_file,
                target_voice=target_voice,
                voice_engine=voice_engine
            )
            
            preview_path = input_file.with_suffix('.preview.mp3')
            with open(preview_path, 'wb') as f:
                f.write(audio_data)
            
            click.echo(f"Preview saved to: {preview_path}")
        else:
            click.echo(f"Processing: {input_file}")
            
            output_path = voice_changer.change_voice(
                input_audio_path=input_file,
                target_voice=target_voice,
                output_path=output,
                voice_engine=voice_engine,
                output_format=output_format,
                sample_rate=int(sample_rate),
                progress_callback=progress_callback
            )
            
            click.echo(f"Voice changed successfully: {output_path}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        logger.error(f"CLI error: {e}")


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True, file_okay=False, path_type=Path))
@click.argument('target_voice', type=str)
@click.argument('output_dir', type=click.Path(path_type=Path))
@click.option('--voice-engine', type=click.Choice(list(VOICE_ENGINES.keys())),
              default=Config.DEFAULT_VOICE_ENGINE, help='Voice engine to use')
@click.option('--format', 'output_format', type=click.Choice(['mp3', 'wav', 'flac', 'ogg']),
              default='mp3', help='Output audio format')
@click.option('--pattern', default='*.wav,*.mp3,*.m4a,*.flac', 
              help='File patterns to process (comma-separated)')
def batch(input_dir, target_voice, output_dir, voice_engine, output_format, pattern):
    """Process multiple audio files in a directory."""
    try:
        # Validate API keys
        is_valid, missing_keys = Config.validate_api_keys()
        if not is_valid:
            click.echo(f"Error: Missing API keys: {', '.join(missing_keys)}", err=True)
            return
        
        # Find input files
        patterns = [p.strip() for p in pattern.split(',')]
        input_files = []
        for pat in patterns:
            input_files.extend(input_dir.glob(pat))
        
        if not input_files:
            click.echo(f"No audio files found in {input_dir}")
            return
        
        click.echo(f"Found {len(input_files)} files to process")
        
        # Initialize voice changer
        voice_changer = VoiceChanger()
        
        # Progress tracking
        with tqdm(total=len(input_files), desc="Processing files") as pbar:
            def progress_callback(message: str, progress: float):
                pbar.set_description(message)
                if progress == 1.0:
                    pbar.update(1)
            
            output_files = voice_changer.batch_change_voice(
                input_files=input_files,
                target_voice=target_voice,
                output_dir=output_dir,
                voice_engine=voice_engine,
                output_format=output_format,
                progress_callback=progress_callback
            )
        
        click.echo(f"Batch processing completed: {len(output_files)} files processed")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        logger.error(f"Batch CLI error: {e}")


@cli.command()
def list_voices():
    """List available voices."""
    try:
        # Validate API keys
        is_valid, missing_keys = Config.validate_api_keys()
        if not is_valid:
            click.echo(f"Error: Missing API keys: {', '.join(missing_keys)}", err=True)
            return
        
        voice_changer = VoiceChanger()
        voices = voice_changer.get_available_voices()
        
        click.echo("\n=== Pre-built Voices ===")
        for voice in voices['prebuilt']:
            click.echo(f"ID: {voice.get('id', 'N/A')}")
            click.echo(f"Name: {voice.get('name', 'N/A')}")
            click.echo(f"Language: {voice.get('language', 'N/A')}")
            click.echo(f"Gender: {voice.get('gender', 'N/A')}")
            click.echo("---")
        
        click.echo("\n=== Cloned Voices ===")
        for voice in voices['cloned']:
            click.echo(f"ID: {voice.get('id', 'N/A')}")
            click.echo(f"Name: {voice.get('name', 'N/A')}")
            click.echo(f"Created: {voice.get('created_at', 'N/A')}")
            click.echo("---")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        logger.error(f"List voices error: {e}")


@cli.command()
@click.argument('sample_audio', type=click.Path(exists=True, path_type=Path))
@click.argument('voice_name', type=str)
def clone_voice(sample_audio, voice_name):
    """Create a voice clone from a sample audio file."""
    try:
        # Validate API keys
        is_valid, missing_keys = Config.validate_api_keys()
        if not is_valid:
            click.echo(f"Error: Missing API keys: {', '.join(missing_keys)}", err=True)
            return
        
        voice_changer = VoiceChanger()
        
        click.echo(f"Creating voice clone '{voice_name}' from {sample_audio}")
        result = voice_changer.create_voice_clone(sample_audio, voice_name)
        
        click.echo(f"Voice clone created successfully!")
        click.echo(f"Voice ID: {result.get('id', 'N/A')}")
        click.echo(f"Voice URL: {result.get('voice_url', 'N/A')}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        logger.error(f"Clone voice error: {e}")


@cli.command()
@click.argument('input_file', type=click.Path(exists=True, path_type=Path))
def estimate_cost(input_file):
    """Estimate the cost of processing an audio file."""
    try:
        # Validate API keys
        is_valid, missing_keys = Config.validate_api_keys()
        if not is_valid:
            click.echo(f"Error: Missing API keys: {', '.join(missing_keys)}", err=True)
            return
        
        voice_changer = VoiceChanger()
        cost_info = voice_changer.estimate_processing_cost(input_file)
        
        click.echo(f"\n=== Cost Estimate for {input_file} ===")
        click.echo(f"Audio Duration: {cost_info['audio_duration_seconds']:.1f} seconds")
        click.echo(f"Estimated Characters: {cost_info['estimated_characters']:,}")
        click.echo(f"Transcription Cost: ${cost_info['transcription_cost_usd']:.4f}")
        click.echo(f"TTS Cost: ${cost_info['tts_cost_usd']:.4f}")
        click.echo(f"Total Estimated Cost: ${cost_info['total_estimated_cost_usd']:.4f}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        logger.error(f"Estimate cost error: {e}")


@cli.command()
def engines():
    """List available voice engines."""
    click.echo("\n=== Available Voice Engines ===")
    for engine, description in VOICE_ENGINES.items():
        click.echo(f"{engine}: {description}")


def main():
    """Main entry point for the CLI."""
    cli()


if __name__ == '__main__':
    main()
