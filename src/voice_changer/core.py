"""Core voice changer functionality."""

import logging
import tempfile
from pathlib import Path
from typing import Optional, Union, Dict, Any, Callable
import time

from .audio_utils import AudioProcessor
from .speech_to_text import SpeechToText
from .playht_client import PlayHTClient
from ..config import Config

logger = logging.getLogger(__name__)


class VoiceChanger:
    """Main voice changer class that orchestrates the entire pipeline."""
    
    def __init__(self, 
                 playht_user_id: Optional[str] = None,
                 playht_api_key: Optional[str] = None,
                 openai_api_key: Optional[str] = None):
        """Initialize the voice changer.
        
        Args:
            playht_user_id: PlayHT user ID
            playht_api_key: PlayHT API key
            openai_api_key: OpenAI API key
        """
        # Initialize components
        self.audio_processor = AudioProcessor()
        self.speech_to_text = SpeechToText(api_key=openai_api_key)
        self.playht_client = PlayHTClient(user_id=playht_user_id, api_key=playht_api_key)
        
        logger.info("Voice changer initialized")
    
    def change_voice(self, 
                    input_audio_path: Union[str, Path],
                    target_voice: str,
                    output_path: Optional[Union[str, Path]] = None,
                    voice_engine: str = None,
                    output_format: str = "mp3",
                    sample_rate: int = 24000,
                    preserve_timing: bool = False,
                    progress_callback: Optional[Callable[[str, float], None]] = None) -> str:
        """Change the voice of an audio file.
        
        Args:
            input_audio_path: Path to input audio file
            target_voice: Target voice ID or URL
            output_path: Output file path (if None, auto-generated)
            voice_engine: PlayHT voice engine to use
            output_format: Output audio format
            sample_rate: Output sample rate
            preserve_timing: Whether to preserve original timing (experimental)
            progress_callback: Callback function for progress updates
            
        Returns:
            str: Path to the output audio file
        """
        input_path = Path(input_audio_path)
        
        if progress_callback:
            progress_callback("Validating input audio...", 0.0)
        
        # Validate input audio
        is_valid, error_msg = self.audio_processor.validate_audio_file(input_path)
        if not is_valid:
            raise ValueError(f"Invalid input audio: {error_msg}")
        
        # Get audio info
        audio_info = self.audio_processor.get_audio_info(input_path)
        logger.info(f"Processing audio: {audio_info}")
        
        if progress_callback:
            progress_callback("Converting audio format...", 0.1)
        
        # Convert to WAV for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_wav_path = Path(temp_dir) / "input.wav"
            wav_path = self.audio_processor.convert_to_wav(input_path, temp_wav_path)
            
            if progress_callback:
                progress_callback("Transcribing speech...", 0.2)
            
            # Transcribe audio to text
            start_time = time.time()
            if preserve_timing:
                transcription_result = self.speech_to_text.transcribe_with_timestamps(wav_path)
                text = transcription_result["text"]
                timestamps = transcription_result.get("words", [])
            else:
                text = self.speech_to_text.transcribe_audio(wav_path)
                timestamps = None
            
            transcription_time = time.time() - start_time
            logger.info(f"Transcription completed in {transcription_time:.2f}s: {len(text)} characters")
            
            if not text.strip():
                raise ValueError("No speech detected in the audio file")
            
            if progress_callback:
                progress_callback("Generating new voice...", 0.6)
            
            # Convert text to speech with target voice
            start_time = time.time()
            audio_data = self.playht_client.text_to_speech(
                text=text,
                voice=target_voice,
                voice_engine=voice_engine or Config.DEFAULT_VOICE_ENGINE,
                format=output_format,
                sample_rate=sample_rate
            )
            tts_time = time.time() - start_time
            logger.info(f"TTS completed in {tts_time:.2f}s: {len(audio_data)} bytes")
            
            if progress_callback:
                progress_callback("Saving output audio...", 0.9)
            
            # Determine output path
            if output_path is None:
                output_path = input_path.with_suffix(f'.voice_changed.{output_format}')
            else:
                output_path = Path(output_path)
            
            # Save the audio
            final_output_path = self.audio_processor.save_audio(
                audio_data=audio_data,
                output_path=output_path,
                format=output_format,
                sample_rate=sample_rate
            )
            
            if progress_callback:
                progress_callback("Voice change completed!", 1.0)
            
            logger.info(f"Voice change completed: {final_output_path}")
            return final_output_path
    
    def batch_change_voice(self,
                          input_files: list[Union[str, Path]],
                          target_voice: str,
                          output_dir: Union[str, Path],
                          voice_engine: str = None,
                          output_format: str = "mp3",
                          progress_callback: Optional[Callable[[str, float], None]] = None) -> list[str]:
        """Change voice for multiple audio files.
        
        Args:
            input_files: List of input audio file paths
            target_voice: Target voice ID or URL
            output_dir: Output directory
            voice_engine: PlayHT voice engine to use
            output_format: Output audio format
            progress_callback: Callback function for progress updates
            
        Returns:
            list: List of output file paths
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_files = []
        total_files = len(input_files)
        
        for i, input_file in enumerate(input_files):
            try:
                input_path = Path(input_file)
                output_path = output_dir / f"{input_path.stem}_voice_changed.{output_format}"
                
                if progress_callback:
                    progress_callback(f"Processing {input_path.name} ({i+1}/{total_files})", i / total_files)
                
                result_path = self.change_voice(
                    input_audio_path=input_path,
                    target_voice=target_voice,
                    output_path=output_path,
                    voice_engine=voice_engine,
                    output_format=output_format
                )
                
                output_files.append(result_path)
                logger.info(f"Processed {input_path.name} -> {result_path}")
                
            except Exception as e:
                logger.error(f"Error processing {input_file}: {e}")
                # Continue with other files
        
        if progress_callback:
            progress_callback(f"Batch processing completed: {len(output_files)}/{total_files} files", 1.0)
        
        return output_files
    
    def preview_voice_change(self,
                           input_audio_path: Union[str, Path],
                           target_voice: str,
                           preview_duration: int = 30,
                           voice_engine: str = None) -> bytes:
        """Generate a preview of voice change (first N seconds).
        
        Args:
            input_audio_path: Path to input audio file
            target_voice: Target voice ID or URL
            preview_duration: Preview duration in seconds
            voice_engine: PlayHT voice engine to use
            
        Returns:
            bytes: Preview audio data
        """
        input_path = Path(input_audio_path)
        
        # Validate input
        is_valid, error_msg = self.audio_processor.validate_audio_file(input_path)
        if not is_valid:
            raise ValueError(f"Invalid input audio: {error_msg}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Trim audio to preview duration
            from pydub import AudioSegment
            audio = AudioSegment.from_file(str(input_path))
            preview_audio = audio[:preview_duration * 1000]  # Convert to milliseconds
            
            preview_path = Path(temp_dir) / "preview.wav"
            preview_audio.export(str(preview_path), format="wav")
            
            # Process the preview
            return self.change_voice(
                input_audio_path=preview_path,
                target_voice=target_voice,
                voice_engine=voice_engine,
                output_format="mp3"
            )
    
    def get_available_voices(self) -> Dict[str, list]:
        """Get all available voices (pre-built and cloned).
        
        Returns:
            dict: Dictionary with 'prebuilt' and 'cloned' voice lists
        """
        try:
            prebuilt_voices = self.playht_client.list_voices()
            cloned_voices = self.playht_client.list_cloned_voices()
            
            return {
                'prebuilt': prebuilt_voices,
                'cloned': cloned_voices
            }
        except Exception as e:
            logger.error(f"Error getting available voices: {e}")
            return {'prebuilt': [], 'cloned': []}
    
    def create_voice_clone(self, sample_audio_path: Union[str, Path], voice_name: str) -> Dict[str, Any]:
        """Create a new voice clone.
        
        Args:
            sample_audio_path: Path to sample audio for cloning
            voice_name: Name for the new voice
            
        Returns:
            dict: Voice clone information
        """
        # Validate sample audio
        is_valid, error_msg = self.audio_processor.validate_audio_file(sample_audio_path)
        if not is_valid:
            raise ValueError(f"Invalid sample audio: {error_msg}")
        
        return self.playht_client.create_voice_clone(sample_audio_path, voice_name)
    
    def estimate_processing_cost(self, input_audio_path: Union[str, Path]) -> Dict[str, float]:
        """Estimate the cost of processing an audio file.
        
        Args:
            input_audio_path: Path to input audio file
            
        Returns:
            dict: Cost estimates for different services
        """
        audio_info = self.audio_processor.get_audio_info(input_audio_path)
        duration_seconds = audio_info.get('duration_seconds', 0)
        
        # Estimate transcription cost (Whisper)
        transcription_cost = self.speech_to_text.get_transcription_cost_estimate(duration_seconds)
        
        # For TTS cost, we need to estimate text length
        # Rough estimate: 150 words per minute, 5 characters per word
        estimated_chars = int((duration_seconds / 60) * 150 * 5)
        tts_cost = self.playht_client.estimate_cost("x" * estimated_chars)
        
        return {
            'transcription_cost_usd': transcription_cost,
            'tts_cost_usd': tts_cost,
            'total_estimated_cost_usd': transcription_cost + tts_cost,
            'estimated_characters': estimated_chars,
            'audio_duration_seconds': duration_seconds
        }
