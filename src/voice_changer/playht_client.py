"""PlayHT API client for text-to-speech and voice management."""

import logging
import time
from pathlib import Path
from typing import Optional, List, Dict, Any, Union, Iterator
import requests
from pyht import Client, AsyncClient, TTSOptions
from pyht.client import Format

from ..config import Config, VOICE_ENGINES

logger = logging.getLogger(__name__)


class PlayHTClient:
    """Client for interacting with PlayHT API."""
    
    def __init__(self, user_id: Optional[str] = None, api_key: Optional[str] = None):
        """Initialize the PlayHT client.
        
        Args:
            user_id: PlayHT user ID (if None, uses config)
            api_key: PlayHT API key (if None, uses config)
        """
        self.user_id = user_id or Config.PLAY_HT_USER_ID
        self.api_key = api_key or Config.PLAY_HT_API_KEY
        
        if not self.user_id or not self.api_key:
            raise ValueError("PlayHT user ID and API key are required")
        
        # Initialize the PyHT client
        self.client = Client(user_id=self.user_id, api_key=self.api_key)
        
        # Base URL for REST API calls
        self.base_url = "https://api.play.ht/api/v2"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "X-User-ID": self.user_id,
            "Content-Type": "application/json"
        }
        
        logger.info("PlayHT client initialized")
    
    def text_to_speech(self, text: str, voice: str, 
                      voice_engine: str = None,
                      format: str = "mp3",
                      sample_rate: int = 24000,
                      speed: float = 1.0,
                      temperature: float = None,
                      **kwargs) -> bytes:
        """Convert text to speech using PlayHT.
        
        Args:
            text: Text to convert to speech
            voice: Voice ID or URL
            voice_engine: Voice engine to use
            format: Audio format (mp3, wav, etc.)
            sample_rate: Sample rate
            speed: Speech speed multiplier
            temperature: Voice temperature
            **kwargs: Additional TTS options
            
        Returns:
            bytes: Audio data
        """
        if not text.strip():
            raise ValueError("Text cannot be empty")
        
        # Set default voice engine
        if voice_engine is None:
            voice_engine = Config.DEFAULT_VOICE_ENGINE
        
        # Map format string to Format enum
        format_map = {
            "mp3": Format.FORMAT_MP3,
            "wav": Format.FORMAT_WAV,
            "flac": Format.FORMAT_FLAC,
            "ogg": Format.FORMAT_OGG,
            "mulaw": Format.FORMAT_MULAW,
            "raw": Format.FORMAT_RAW
        }
        
        audio_format = format_map.get(format.lower(), Format.FORMAT_MP3)
        
        # Create TTS options
        options = TTSOptions(
            voice=voice,
            format=audio_format,
            sample_rate=sample_rate,
            speed=speed
        )
        
        # Add optional parameters
        if temperature is not None:
            options.temperature = temperature
        
        # Add any additional kwargs to options
        for key, value in kwargs.items():
            if hasattr(options, key):
                setattr(options, key, value)
        
        try:
            logger.info(f"Converting text to speech: {len(text)} characters")
            
            # Collect audio chunks
            audio_chunks = []
            for chunk in self.client.tts(text, options, voice_engine=voice_engine):
                audio_chunks.append(chunk)
            
            # Combine all chunks
            audio_data = b''.join(audio_chunks)
            
            logger.info(f"TTS completed. Audio size: {len(audio_data)} bytes")
            return audio_data
            
        except Exception as e:
            logger.error(f"Error in text-to-speech: {e}")
            raise
    
    def stream_text_to_speech(self, text: str, voice: str,
                             voice_engine: str = None,
                             **kwargs) -> Iterator[bytes]:
        """Stream text-to-speech conversion.
        
        Args:
            text: Text to convert
            voice: Voice ID or URL
            voice_engine: Voice engine to use
            **kwargs: Additional TTS options
            
        Yields:
            bytes: Audio chunks
        """
        if voice_engine is None:
            voice_engine = Config.DEFAULT_VOICE_ENGINE
        
        options = TTSOptions(voice=voice, **kwargs)
        
        try:
            for chunk in self.client.tts(text, options, voice_engine=voice_engine):
                yield chunk
        except Exception as e:
            logger.error(f"Error in streaming TTS: {e}")
            raise
    
    def list_voices(self) -> List[Dict[str, Any]]:
        """List available pre-built voices.
        
        Returns:
            list: List of available voices
        """
        try:
            url = f"{self.base_url}/voices"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            voices = response.json()
            logger.info(f"Retrieved {len(voices)} voices")
            return voices
            
        except Exception as e:
            logger.error(f"Error listing voices: {e}")
            raise
    
    def list_cloned_voices(self) -> List[Dict[str, Any]]:
        """List user's cloned voices.
        
        Returns:
            list: List of cloned voices
        """
        try:
            url = f"{self.base_url}/cloned-voices"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            voices = response.json()
            logger.info(f"Retrieved {len(voices)} cloned voices")
            return voices
            
        except Exception as e:
            logger.error(f"Error listing cloned voices: {e}")
            raise
    
    def create_voice_clone(self, audio_file_path: Union[str, Path],
                          voice_name: str) -> Dict[str, Any]:
        """Create a voice clone from an audio file.
        
        Args:
            audio_file_path: Path to audio file for cloning
            voice_name: Name for the cloned voice
            
        Returns:
            dict: Voice clone information
        """
        audio_file_path = Path(audio_file_path)
        
        if not audio_file_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_file_path}")
        
        try:
            url = f"{self.base_url}/cloned-voices/instant"
            
            # Prepare multipart form data
            files = {
                'sample_file': (audio_file_path.name, open(audio_file_path, 'rb'), 'audio/wav')
            }
            data = {
                'voice_name': voice_name
            }
            
            # Remove Content-Type header for multipart upload
            headers = {k: v for k, v in self.headers.items() if k != 'Content-Type'}
            
            response = requests.post(url, headers=headers, files=files, data=data)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Voice clone created: {voice_name}")
            return result
            
        except Exception as e:
            logger.error(f"Error creating voice clone: {e}")
            raise
        finally:
            # Close file if it was opened
            if 'sample_file' in locals():
                files['sample_file'][1].close()
    
    def delete_voice_clone(self, voice_id: str) -> bool:
        """Delete a cloned voice.
        
        Args:
            voice_id: ID of the voice to delete
            
        Returns:
            bool: True if successful
        """
        try:
            url = f"{self.base_url}/cloned-voices/{voice_id}"
            response = requests.delete(url, headers=self.headers)
            response.raise_for_status()
            
            logger.info(f"Voice clone deleted: {voice_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting voice clone: {e}")
            return False
    
    def get_voice_info(self, voice_id: str) -> Dict[str, Any]:
        """Get information about a specific voice.
        
        Args:
            voice_id: Voice ID
            
        Returns:
            dict: Voice information
        """
        try:
            # Try cloned voices first
            cloned_voices = self.list_cloned_voices()
            for voice in cloned_voices:
                if voice.get('id') == voice_id:
                    return voice
            
            # Then try pre-built voices
            voices = self.list_voices()
            for voice in voices:
                if voice.get('id') == voice_id:
                    return voice
            
            raise ValueError(f"Voice not found: {voice_id}")
            
        except Exception as e:
            logger.error(f"Error getting voice info: {e}")
            raise
    
    def estimate_cost(self, text: str) -> float:
        """Estimate the cost of TTS conversion.
        
        Args:
            text: Text to convert
            
        Returns:
            float: Estimated cost in USD
        """
        # PlayHT pricing varies by plan, this is a rough estimate
        # Typically around $0.0004 per 1000 characters
        character_count = len(text)
        cost_per_1000_chars = 0.0004
        return (character_count / 1000) * cost_per_1000_chars
