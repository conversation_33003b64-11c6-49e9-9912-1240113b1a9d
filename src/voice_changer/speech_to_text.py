"""Speech-to-text functionality using OpenAI Whisper."""

import logging
from pathlib import Path
from typing import Optional, Union, Dict, Any
import openai
from openai import OpenAI

from ..config import Config

logger = logging.getLogger(__name__)


class SpeechToText:
    """Handles speech-to-text conversion using OpenAI Whisper."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the speech-to-text client.
        
        Args:
            api_key: OpenAI API key (if None, uses config)
        """
        self.api_key = api_key or Config.OPENAI_API_KEY
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        self.client = OpenAI(api_key=self.api_key)
        logger.info("Speech-to-text client initialized")
    
    def transcribe_audio(self, audio_path: Union[str, Path], 
                        language: Optional[str] = None,
                        prompt: Optional[str] = None,
                        temperature: float = 0.0,
                        response_format: str = "text") -> str:
        """Transcribe audio file to text.
        
        Args:
            audio_path: Path to audio file
            language: Language code (e.g., 'en', 'es', 'fr')
            prompt: Optional prompt to guide transcription
            temperature: Sampling temperature (0-1)
            response_format: Response format ('text', 'json', 'verbose_json')
            
        Returns:
            str: Transcribed text
        """
        audio_path = Path(audio_path)
        
        if not audio_path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        try:
            with open(audio_path, 'rb') as audio_file:
                logger.info(f"Transcribing audio: {audio_path}")
                
                # Prepare transcription parameters
                transcription_params = {
                    "file": audio_file,
                    "model": "whisper-1",
                    "temperature": temperature,
                    "response_format": response_format
                }
                
                # Add optional parameters
                if language:
                    transcription_params["language"] = language
                if prompt:
                    transcription_params["prompt"] = prompt
                
                # Perform transcription
                response = self.client.audio.transcriptions.create(**transcription_params)
                
                # Extract text based on response format
                if response_format == "text":
                    text = response
                else:
                    text = response.text
                
                logger.info(f"Transcription completed. Length: {len(text)} characters")
                return text.strip()
                
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            raise
    
    def transcribe_with_timestamps(self, audio_path: Union[str, Path],
                                 language: Optional[str] = None) -> Dict[str, Any]:
        """Transcribe audio with word-level timestamps.
        
        Args:
            audio_path: Path to audio file
            language: Language code
            
        Returns:
            dict: Transcription with timestamps
        """
        try:
            with open(audio_path, 'rb') as audio_file:
                logger.info(f"Transcribing with timestamps: {audio_path}")
                
                transcription_params = {
                    "file": audio_file,
                    "model": "whisper-1",
                    "response_format": "verbose_json",
                    "timestamp_granularities": ["word"]
                }
                
                if language:
                    transcription_params["language"] = language
                
                response = self.client.audio.transcriptions.create(**transcription_params)
                
                return {
                    "text": response.text,
                    "language": response.language,
                    "duration": response.duration,
                    "words": response.words if hasattr(response, 'words') else [],
                    "segments": response.segments if hasattr(response, 'segments') else []
                }
                
        except Exception as e:
            logger.error(f"Error transcribing with timestamps: {e}")
            raise
    
    def detect_language(self, audio_path: Union[str, Path]) -> str:
        """Detect the language of the audio.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            str: Detected language code
        """
        try:
            # Use transcription with verbose response to get language
            result = self.transcribe_with_timestamps(audio_path)
            return result.get("language", "en")
            
        except Exception as e:
            logger.error(f"Error detecting language: {e}")
            return "en"  # Default to English
    
    def translate_to_english(self, audio_path: Union[str, Path],
                           prompt: Optional[str] = None,
                           temperature: float = 0.0) -> str:
        """Translate audio to English text.
        
        Args:
            audio_path: Path to audio file
            prompt: Optional prompt to guide translation
            temperature: Sampling temperature
            
        Returns:
            str: Translated English text
        """
        audio_path = Path(audio_path)
        
        try:
            with open(audio_path, 'rb') as audio_file:
                logger.info(f"Translating audio to English: {audio_path}")
                
                translation_params = {
                    "file": audio_file,
                    "model": "whisper-1",
                    "temperature": temperature,
                    "response_format": "text"
                }
                
                if prompt:
                    translation_params["prompt"] = prompt
                
                response = self.client.audio.translations.create(**translation_params)
                
                logger.info(f"Translation completed. Length: {len(response)} characters")
                return response.strip()
                
        except Exception as e:
            logger.error(f"Error translating audio: {e}")
            raise
    
    def get_transcription_cost_estimate(self, audio_duration_seconds: float) -> float:
        """Estimate the cost of transcription based on audio duration.
        
        Args:
            audio_duration_seconds: Duration of audio in seconds
            
        Returns:
            float: Estimated cost in USD
        """
        # OpenAI Whisper pricing: $0.006 per minute
        cost_per_minute = 0.006
        duration_minutes = audio_duration_seconds / 60
        return duration_minutes * cost_per_minute
