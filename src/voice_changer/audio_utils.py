"""Audio processing utilities for the voice changer application."""

import os
import logging
from pathlib import Path
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Union
import soundfile as sf
import numpy as np
from pydub import AudioSegment
from pydub.utils import which

from ..config import Config, SUPPORTED_INPUT_FORMATS, SUPPORTED_OUTPUT_FORMATS

logger = logging.getLogger(__name__)


class AudioProcessor:
    """Handles audio file processing, validation, and conversion."""
    
    def __init__(self):
        """Initialize the audio processor."""
        # Check if ffmpeg is available for pydub
        if not which("ffmpeg"):
            logger.warning("ffmpeg not found. Some audio formats may not be supported.")
    
    def validate_audio_file(self, file_path: Union[str, Path]) -> Tuple[bool, str]:
        """Validate an audio file for processing.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            tuple: (is_valid, error_message)
        """
        file_path = Path(file_path)
        
        # Check if file exists
        if not file_path.exists():
            return False, f"File does not exist: {file_path}"
        
        # Check file extension
        if file_path.suffix.lower() not in SUPPORTED_INPUT_FORMATS:
            return False, f"Unsupported file format: {file_path.suffix}"
        
        # Check file size
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > Config.MAX_FILE_SIZE_MB:
            return False, f"File too large: {file_size_mb:.1f}MB (max: {Config.MAX_FILE_SIZE_MB}MB)"
        
        try:
            # Load audio to check duration and validity
            audio = AudioSegment.from_file(str(file_path))
            duration_seconds = len(audio) / 1000
            
            if duration_seconds > Config.MAX_AUDIO_DURATION_SECONDS:
                return False, f"Audio too long: {duration_seconds:.1f}s (max: {Config.MAX_AUDIO_DURATION_SECONDS}s)"
            
            if duration_seconds < 1:
                return False, "Audio too short: minimum 1 second required"
                
        except Exception as e:
            return False, f"Invalid audio file: {str(e)}"
        
        return True, ""
    
    def get_audio_info(self, file_path: Union[str, Path]) -> dict:
        """Get information about an audio file.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            dict: Audio file information
        """
        try:
            audio = AudioSegment.from_file(str(file_path))
            file_size_mb = Path(file_path).stat().st_size / (1024 * 1024)
            
            return {
                'duration_seconds': len(audio) / 1000,
                'sample_rate': audio.frame_rate,
                'channels': audio.channels,
                'file_size_mb': file_size_mb,
                'format': Path(file_path).suffix.lower(),
                'frame_count': audio.frame_count(),
                'sample_width': audio.sample_width
            }
        except Exception as e:
            logger.error(f"Error getting audio info: {e}")
            return {}
    
    def convert_to_wav(self, input_path: Union[str, Path], output_path: Optional[Union[str, Path]] = None) -> str:
        """Convert audio file to WAV format for processing.
        
        Args:
            input_path: Path to input audio file
            output_path: Optional output path (if None, creates temp file)
            
        Returns:
            str: Path to the converted WAV file
        """
        input_path = Path(input_path)
        
        if output_path is None:
            output_path = input_path.with_suffix('.wav')
        else:
            output_path = Path(output_path)
        
        try:
            audio = AudioSegment.from_file(str(input_path))
            # Convert to mono if stereo (for better speech recognition)
            if audio.channels > 1:
                audio = audio.set_channels(1)
            
            # Export as WAV
            audio.export(str(output_path), format="wav")
            logger.info(f"Converted {input_path} to {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error converting audio: {e}")
            raise
    
    def normalize_audio(self, audio_data: np.ndarray, target_level: float = -20.0) -> np.ndarray:
        """Normalize audio to a target level.
        
        Args:
            audio_data: Audio data as numpy array
            target_level: Target level in dB
            
        Returns:
            np.ndarray: Normalized audio data
        """
        # Calculate RMS
        rms = np.sqrt(np.mean(audio_data ** 2))
        
        if rms > 0:
            # Convert target level from dB to linear
            target_linear = 10 ** (target_level / 20)
            # Calculate scaling factor
            scale_factor = target_linear / rms
            # Apply scaling
            normalized = audio_data * scale_factor
            # Prevent clipping
            normalized = np.clip(normalized, -1.0, 1.0)
            return normalized
        
        return audio_data
    
    def save_audio(self, audio_data: bytes, output_path: Union[str, Path], 
                   format: str = "mp3", sample_rate: int = 24000) -> str:
        """Save audio data to file.
        
        Args:
            audio_data: Raw audio data
            output_path: Output file path
            format: Output format (mp3, wav, etc.)
            sample_rate: Sample rate
            
        Returns:
            str: Path to saved file
        """
        output_path = Path(output_path)
        
        try:
            # Write raw audio data to temporary file
            temp_path = output_path.with_suffix('.tmp')
            with open(temp_path, 'wb') as f:
                f.write(audio_data)
            
            # Convert to desired format using pydub
            audio = AudioSegment.from_file(str(temp_path))
            audio.export(str(output_path), format=format)
            
            # Clean up temp file
            temp_path.unlink()
            
            logger.info(f"Saved audio to {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error saving audio: {e}")
            raise
    
    def trim_silence(self, audio_path: Union[str, Path], 
                     silence_thresh: int = -40, chunk_size: int = 10) -> str:
        """Trim silence from the beginning and end of audio.
        
        Args:
            audio_path: Path to audio file
            silence_thresh: Silence threshold in dB
            chunk_size: Chunk size in milliseconds
            
        Returns:
            str: Path to trimmed audio file
        """
        try:
            audio = AudioSegment.from_file(str(audio_path))
            
            # Trim silence
            trimmed = audio.strip_silence(
                silence_len=chunk_size,
                silence_thresh=silence_thresh
            )
            
            # Save trimmed audio
            output_path = Path(audio_path).with_suffix('.trimmed.wav')
            trimmed.export(str(output_path), format="wav")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error trimming silence: {e}")
            return str(audio_path)  # Return original if trimming fails
