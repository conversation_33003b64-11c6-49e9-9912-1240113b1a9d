[project]
name = "play-ht"
version = "0.1.0"
description = "Voice changer application using PlayHT API for high-quality voice transformation"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "notebook>=7.4.3",
    "pyht>=0.0.28",
    "openai>=1.0.0",
    "pydub>=0.25.1",
    "streamlit>=1.28.0",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
    "soundfile>=0.12.1",
    "numpy>=1.24.0",
    "click>=8.1.0",
    "tqdm>=4.66.0",
    "aiohttp>=3.8.0",
    "websockets>=11.0.0",
]

[project.scripts]
voice-changer = "src.cli:main"
voice-changer-web = "src.web_app:main"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.setuptools.package-dir]
"" = "."
