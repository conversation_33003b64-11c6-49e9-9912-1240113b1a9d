"""Main entry point for the voice changer application."""

import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config
from src.cli import main as cli_main


def main():
    """Main function that provides options to run CLI or web app."""
    print("🎤 Voice Changer Application")
    print("=" * 40)

    # Setup logging
    Config.setup_logging()
    logger = logging.getLogger(__name__)

    # Check API keys
    is_valid, missing_keys = Config.validate_api_keys()
    if not is_valid:
        print(f"❌ Missing API keys: {', '.join(missing_keys)}")
        print("Please set the required environment variables or create a .env file.")
        print("\nRequired environment variables:")
        print("- PLAY_HT_USER_ID")
        print("- PLAY_HT_API_KEY")
        print("- OPENAI_API_KEY")
        return

    print("✅ API keys configured")
    print("\nAvailable options:")
    print("1. Run CLI interface")
    print("2. Run web interface")
    print("3. Quick demo")

    try:
        choice = input("\nEnter your choice (1-3): ").strip()

        if choice == "1":
            print("\nStarting CLI interface...")
            print("Use 'python -m src.cli --help' for available commands")
            cli_main()
        elif choice == "2":
            print("\nStarting web interface...")
            print("Run: streamlit run src/web_app.py")
            import subprocess
            subprocess.run([sys.executable, "-m", "streamlit", "run", "src/web_app.py"])
        elif choice == "3":
            print("\nQuick Demo:")
            print("This would demonstrate basic voice changing functionality")
            print("For now, please use the CLI or web interface")
        else:
            print("Invalid choice. Please run again and select 1, 2, or 3.")

    except KeyboardInterrupt:
        print("\n\nGoodbye!")
    except Exception as e:
        logger.error(f"Error in main: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
